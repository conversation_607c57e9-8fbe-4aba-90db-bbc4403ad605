# 🧪 Admin SSE Test Commands

## 🎯 **Quick Test cho Admin SSE**

### **Bước 1: Get Admin Token**
```bash
# Login admin để lấy token
curl -X POST http://localhost:3003/v1/auth/admin/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "your_admin_password"
  }'
```

**Response sẽ có dạng:**
```json
{
  "success": true,
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "...",
    "employee": {
      "id": 1,
      "email": "<EMAIL>"
    }
  }
}
```

### **Bước 2: Test Admin SSE Connection**
```bash
# Replace YOUR_ADMIN_TOKEN với token từ bước 1
# Replace WORKFLOW_ID với workflow ID thực tế
curl -N -H "Accept: text/event-stream" \
  "http://localhost:3003/v1/admin/workflows/sse/workflows/6c340d9f-ba0b-44b4-8497-676e052dd773/stream?token=YOUR_ADMIN_TOKEN"
```

### **Bước 3: Test với Postman**

#### **Setup Postman Request:**
1. **Method**: GET
2. **URL**: `http://localhost:3003/v1/admin/workflows/sse/workflows/6c340d9f-ba0b-44b4-8497-676e052dd773/stream`
3. **Query Parameters**:
   - Key: `token`
   - Value: `YOUR_ADMIN_TOKEN`
4. **Headers**:
   - `Accept`: `text/event-stream`
   - `Cache-Control`: `no-cache`

#### **Expected Response:**
```
HTTP/1.1 200 OK
Content-Type: text/event-stream
Cache-Control: no-cache
Connection: keep-alive

data: {"type":"connection.established","data":{"clientId":"admin_1_1706612345_abc","employeeId":1,"workflowId":"6c340d9f-ba0b-44b4-8497-676e052dd773","timestamp":"2025-07-30T07:04:39.000Z"}}

data: {"type":"workflow.started","data":{"workflowId":"6c340d9f-ba0b-44b4-8497-676e052dd773","employeeId":1,"timestamp":"2025-07-30T07:05:00.000Z"}}
```

## 🔧 **Troubleshooting**

### **Lỗi 401 Unauthorized:**
```json
{
  "error": "Unauthorized",
  "message": "Invalid or expired admin token"
}
```

**Giải pháp:**
1. Kiểm tra admin token có hợp lệ không
2. Đảm bảo token chưa hết hạn
3. Verify admin có quyền truy cập

### **Lỗi 404 Not Found:**
```json
{
  "error": {
    "code": 9999,
    "message": "Cannot GET /v1/admin/workflows/sse/workflows/.../stream"
  }
}
```

**Giải pháp:**
1. ✅ **Đã fix**: Endpoint `/stream` đã được thêm vào admin controller
2. Restart server để load changes
3. Verify URL path chính xác

### **Lỗi 500 Internal Server Error:**
```json
{
  "error": "Internal Server Error",
  "message": "Failed to establish SSE connection"
}
```

**Giải pháp:**
1. Check server logs để xem chi tiết lỗi
2. Verify workflow ID tồn tại trong database
3. Check WorkflowSSEAdminService hoạt động bình thường

## 📊 **Verify Implementation**

### **Check Server Logs:**
```bash
# Monitor logs khi test
tail -f logs/application.log | grep -E "(SSE|admin|workflow|6c340d9f)"
```

**Expected logs:**
```
[WorkflowSSEAdminController] Creating admin SSE connection with query token for workflow 6c340d9f-ba0b-44b4-8497-676e052dd773
[WorkflowSSEAdminController] Admin token validated successfully for employee 1
[WorkflowSSEAdminService] Creating SSE connection for employee 1 - workflow 6c340d9f-ba0b-44b4-8497-676e052dd773
[WorkflowSSEAdminController] Admin SSE connection created with query token for employee 1, workflow 6c340d9f-ba0b-44b4-8497-676e052dd773, client: admin_1_1706612345_abc
```

### **Test với Browser Console:**
```javascript
// Paste vào browser console
const adminToken = 'YOUR_ADMIN_TOKEN_HERE';
const workflowId = '6c340d9f-ba0b-44b4-8497-676e052dd773';

const sse = new EventSource(
  `http://localhost:3003/v1/admin/workflows/sse/workflows/${workflowId}/stream?token=${adminToken}`
);

sse.onopen = () => console.log('✅ Admin SSE Connected');
sse.onmessage = (e) => console.log('📡 Admin Event:', JSON.parse(e.data));
sse.onerror = (e) => console.error('❌ Admin SSE Error:', e);

// To close: sse.close();
```

## 🎉 **Success Indicators**

1. ✅ **200 OK Response** từ SSE endpoint
2. ✅ **Connection established event** được nhận
3. ✅ **Server logs** hiển thị connection success
4. ✅ **No error messages** trong console/logs
5. ✅ **Real-time events** khi có workflow activity

## 🚀 **Next Steps After Success**

1. **Trigger workflow execution** để test real-time events
2. **Monitor SSE events** trong browser/Postman
3. **Test multiple connections** để verify scalability
4. **Test connection cleanup** khi close browser/client

## 📝 **Implementation Summary**

✅ **Admin SSE endpoint added**: `/v1/admin/workflows/sse/workflows/{workflowId}/stream`  
✅ **Query token authentication**: `?token={admin_jwt_token}`  
✅ **Admin token validation**: Using `verifyEmployeeAccessToken()`  
✅ **Error handling**: Proper 401/500 responses  
✅ **Logging**: Comprehensive debug logs  
✅ **EventSource compatible**: Works with browser EventSource API  

**Endpoint bây giờ sẽ hoạt động với admin token!** 🎯
