import { Injectable, Logger, Inject } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { firstValueFrom } from 'rxjs';
import { UserNodeExecuteDto, UserWorkflowExecuteDto } from '../dto/execution/user-node-execute.dto';

export interface RedisWorkflowCommand {
  command: 'execute_node' | 'execute_workflow' | 'cancel_execution';
  workflowId: string;
  nodeId?: string;
  executionId: string;
  userId: number;
  data?: any;
  timestamp: string;
}

export interface RedisWorkflowEvent {
  type: 'node.started' | 'node.completed' | 'node.failed' | 'node.progress' | 'workflow.completed' | 'workflow.failed' | 'test_progress';
  workflowId?: string;
  nodeId?: string;
  executionId?: string;
  userId?: number;
  data?: any;
  progress?: number;
  error?: string;
  timestamp?: string;
  // Thêm fields từ BE Worker payload
  nodeResult?: {
    success: boolean;
    output: any;
    executionTime: number;
  };
  // Cho phép any additional fields từ worker
  [key: string]: any;
}

/**
 * Service để handle communication với Redis cho workflow execution
 * <PERSON><PERSON><PERSON> commands đến worker và nhận events từ worker
 */
@Injectable()
export class WorkflowRedisService {
  private readonly logger = new Logger(WorkflowRedisService.name);

  constructor(
    @Inject('REDIS_CLIENT') private readonly redisClient: ClientProxy,
  ) {}

  /**
   * Gửi command đến Redis để worker xử lý
   */
  async sendCommand(command: RedisWorkflowCommand): Promise<any> {
    try {
      this.logger.debug(`Sending command to Redis: ${command.command}`, command);

      const result = await firstValueFrom(
        this.redisClient.send({ cmd: 'workflow_command' }, command)
      );

      this.logger.debug(`Command sent successfully: ${command.command}`, result);
      return result;
    } catch (error) {
      this.logger.error(`Failed to send command: ${command.command}`, error);
      throw error;
    }
  }

  /**
   * Gửi node execution command đến worker
   */
  async executeNode(
    workflowId: string,
    nodeId: string,
    executionId: string,
    userId: number,
    nodeData?: any,
  ): Promise<any> {
    const command: RedisWorkflowCommand = {
      command: 'execute_node',
      workflowId,
      nodeId,
      executionId,
      userId,
      data: nodeData,
      timestamp: new Date().toISOString(),
    };

    return this.sendCommand(command);
  }

  /**
   * Gửi workflow execution command đến worker
   */
  async executeWorkflow(
    workflowId: string,
    executionId: string,
    userId: number,
    workflowData?: any,
  ): Promise<any> {
    const command: RedisWorkflowCommand = {
      command: 'execute_workflow',
      workflowId,
      executionId,
      userId,
      data: workflowData,
      timestamp: new Date().toISOString(),
    };

    return this.sendCommand(command);
  }

  /**
   * Cancel workflow/node execution
   */
  async cancelExecution(
    workflowId: string,
    executionId: string,
    userId: number,
    nodeId?: string,
  ): Promise<any> {
    const command: RedisWorkflowCommand = {
      command: 'cancel_execution',
      workflowId,
      nodeId,
      executionId,
      userId,
      timestamp: new Date().toISOString(),
    };

    return this.sendCommand(command);
  }

  /**
   * Publish event đến Redis channel
   */
  async publishEvent(channel: string, event: RedisWorkflowEvent): Promise<void> {
    try {
      await firstValueFrom(
        this.redisClient.send({ cmd: 'publish' }, {
          channel,
          event,
        })
      );

      this.logger.debug(`Published event to ${channel}:`, event);
    } catch (error) {
      this.logger.error(`Failed to publish event to ${channel}:`, error);
    }
  }

  /**
   * Subscribe đến Redis channel để nhận events
   */
  subscribeToEvents(channel: string, callback: (event: RedisWorkflowEvent) => void): void {
    try {
      this.redisClient.send({ cmd: 'subscribe' }, channel).subscribe({
        next: (data) => {
          try {
            // Parse event data từ Redis
            const event: RedisWorkflowEvent = {
              type: data.type,
              workflowId: data.workflowId,
              nodeId: data.nodeId,
              executionId: data.executionId,
              userId: data.userId,
              data: data.data,
              progress: data.progress,
              error: data.error,
              timestamp: data.timestamp || new Date().toISOString(),
            };

            callback(event);
          } catch (parseError) {
            this.logger.error('Failed to parse Redis event:', parseError);
          }
        },
        error: (error) => {
          this.logger.error(`Redis subscription error for ${channel}:`, error);
        },
      });

      this.logger.log(`Subscribed to Redis channel: ${channel}`);
    } catch (error) {
      this.logger.error(`Failed to subscribe to ${channel}:`, error);
    }
  }

  /**
   * Gửi webhook data đến worker để xử lý
   */
  async processWebhook(
    webhookId: string,
    workflowId: string,
    nodeId: string,
    userId: number,
    webhookData: any,
  ): Promise<any> {
    const executionId = `webhook_${webhookId}_${Date.now()}`;

    const command: RedisWorkflowCommand = {
      command: 'execute_node',
      workflowId,
      nodeId,
      executionId,
      userId,
      data: {
        trigger: 'webhook',
        webhookId,
        webhookData,
        receivedAt: new Date().toISOString(),
      },
      timestamp: new Date().toISOString(),
    };

    return this.sendCommand(command);
  }

  // ========== NEW METHODS WITH UPDATED PAYLOAD FORMAT ==========

  /**
   * Execute single node với new format
   */
  async executeUserNode(
    userId: number,
    workflowId: string,
    nodeId: string,
    type: 'test' | 'execute',
    inputData?: Record<string, any>
  ): Promise<any> {
    const payload: UserNodeExecuteDto = {
      userId,
      workflowId,
      nodeId,
      type,
      inputData
    };

    this.logger.debug(`Sending user_execute_node command:`, payload);

    return await firstValueFrom(
      this.redisClient.send({ cmd: 'user_execute_node' }, payload)
    );
  }

  /**
   * Execute workflow với new format
   */
  async executeUserWorkflow(
    userId: number,
    workflowId: string,
    type: 'test' | 'execute',
    startFromNodeId?: string,
    inputData?: Record<string, any>
  ): Promise<any> {
    const payload: UserWorkflowExecuteDto = {
      userId,
      workflowId,
      nodeId: startFromNodeId || null,
      type,
      inputData
    };

    this.logger.debug(`Sending user_execute_workflow command:`, payload);

    return await firstValueFrom(
      this.redisClient.send({ cmd: 'user_execute_workflow' }, payload)
    );
  }

  /**
   * Process webhook với new format
   */
  async processWebhookNew(
    webhookId: string,
    workflowId: string,
    nodeId: string,
    userId: number,
    webhookData: any,
  ): Promise<any> {
    // Transform webhook data to inputData format
    const inputData = {
      ...webhookData,
      _trigger: {
        type: 'webhook',
        webhookId,
        receivedAt: new Date().toISOString()
      }
    };

    this.logger.debug(`Processing webhook with new format:`, { webhookId, workflowId, nodeId });

    return this.executeUserNode(
      userId,
      workflowId,
      nodeId,
      'execute',
      inputData
    );
  }

  /**
   * Process schedule với new format
   */
  async processScheduleNew(
    workflowId: string,
    nodeId: string,
    userId: number,
    scheduleData: any,
  ): Promise<any> {
    const inputData = {
      ...scheduleData,
      _trigger: {
        type: 'schedule',
        triggeredAt: new Date().toISOString()
      }
    };

    this.logger.debug(`Processing schedule with new format:`, { workflowId, nodeId });

    return this.executeUserNode(
      userId,
      workflowId,
      nodeId,
      'execute',
      inputData
    );
  }

  /**
   * Get execution status từ Redis
   */
  async getExecutionStatus(executionId: string): Promise<any> {
    try {
      const result = await firstValueFrom(
        this.redisClient.send({ cmd: 'get_execution_status' }, { executionId })
      );

      return result;
    } catch (error) {
      this.logger.error(`Failed to get execution status for ${executionId}:`, error);
      return null;
    }
  }

  /**
   * Get workflow execution history từ Redis
   */
  async getExecutionHistory(workflowId: string, limit: number = 10): Promise<any[]> {
    try {
      const result = await firstValueFrom(
        this.redisClient.send({ cmd: 'get_execution_history' }, { workflowId, limit })
      );

      return result || [];
    } catch (error) {
      this.logger.error(`Failed to get execution history for ${workflowId}:`, error);
      return [];
    }
  }

  /**
   * Health check Redis connection
   */
  async healthCheck(): Promise<boolean> {
    try {
      const result = await firstValueFrom(
        this.redisClient.send({ cmd: 'ping' }, {})
      );

      return result === 'pong';
    } catch (error) {
      this.logger.error('Redis health check failed:', error);
      return false;
    }
  }
}
