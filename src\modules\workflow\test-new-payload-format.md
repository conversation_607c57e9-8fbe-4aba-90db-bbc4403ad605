# Test New Payload Format Implementation

## 🎯 **Overview**

<PERSON><PERSON> implement new payload format với:
- `UserNodeExecuteDto` và `UserWorkflowExecuteDto`
- Message patterns: `user_execute_node` và `user_execute_workflow`
- Support `inputData` field cho webhook/schedule data

## 📋 **Testing Steps**

### **1. Test Webhook Trigger với New Format**

#### **A. Setup Test Data:**
```sql
-- 1. Tạo workflow
INSERT INTO workflows (id, name, user_id) 
VALUES ('wf-test-new-123', 'New Format Test Workflow', 1);

-- 2. Tạo webhook node
INSERT INTO nodes (id, workflow_id, node_definition_id, name) 
VALUES ('node-webhook-new-123', 'wf-test-new-123', 'webhook-def-id', 'New Format Webhook');

-- 3. Tạo webhook registry
INSERT INTO webhook_registry (id, webhook_name, node_id, workflow_id) 
VALUES ('wh-test-new-123', 'new-format-test', 'node-webhook-new-123', 'wf-test-new-123');
```

#### **B. Test Webhook Call:**
```bash
curl -X POST http://localhost:3000/webhook/wh-test-new-123 \
  -H "Content-Type: application/json" \
  -d '{
    "event": "payment.completed",
    "amount": 100.00,
    "customer": {
      "id": "cust_123",
      "email": "<EMAIL>"
    },
    "metadata": {
      "source": "stripe",
      "test_mode": true
    }
  }'
```

#### **C. Expected Worker Payload:**
```json
{
  "cmd": "user_execute_node",
  "payload": {
    "userId": 1,
    "workflowId": "wf-test-new-123",
    "nodeId": "node-webhook-new-123",
    "type": "execute",
    "inputData": {
      "event": "payment.completed",
      "amount": 100.00,
      "customer": {
        "id": "cust_123",
        "email": "<EMAIL>"
      },
      "metadata": {
        "source": "stripe",
        "test_mode": true
      },
      "_trigger": {
        "type": "webhook",
        "webhookId": "wh-test-new-123",
        "receivedAt": "2025-01-30T10:00:00.000Z"
      }
    }
  }
}
```

### **2. Test Manual Node Execution với Input Data**

```bash
curl -X POST http://localhost:3000/api/user/workflow/wf-test-new-123/node/node-regular-123/execute \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "inputData": {
      "customField": "test value",
      "parameters": {
        "temperature": 0.7,
        "maxTokens": 1000
      }
    }
  }'
```

#### **Expected Worker Payload:**
```json
{
  "cmd": "user_execute_node",
  "payload": {
    "userId": 1,
    "workflowId": "wf-test-new-123",
    "nodeId": "node-regular-123",
    "type": "execute",
    "inputData": {
      "customField": "test value",
      "parameters": {
        "temperature": 0.7,
        "maxTokens": 1000
      }
    }
  }
}
```

### **3. Test Workflow Execution**

```bash
curl -X POST http://localhost:3000/api/user/workflow/wf-test-new-123/execute \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

#### **Expected Worker Payload:**
```json
{
  "cmd": "user_execute_workflow",
  "payload": {
    "userId": 1,
    "workflowId": "wf-test-new-123",
    "nodeId": null,
    "type": "execute",
    "inputData": undefined
  }
}
```

## 🔧 **Implementation Changes**

### **1. New DTOs:**
- ✅ `UserNodeExecuteDto` - với `inputData` field
- ✅ `UserWorkflowExecuteDto` - với `inputData` field

### **2. Updated Services:**
- ✅ `WorkflowRedisService` - new methods với correct message patterns
- ✅ `WorkflowExecutionUserService` - support input data parameter
- ✅ `WebhookTriggerController` - use new format

### **3. Message Patterns:**
- ✅ `{ cmd: 'user_execute_node' }` thay vì `{ cmd: 'workflow_command' }`
- ✅ `{ cmd: 'user_execute_workflow' }` thay vì `{ cmd: 'process_workflow' }`

### **4. Data Structure:**
- ✅ Webhook data trong `inputData` thay vì nested `data.webhookData`
- ✅ Schedule data trong `inputData` với `_trigger` metadata
- ✅ Clean, consistent structure cho worker

## 🚨 **Migration Notes**

### **Backward Compatibility:**
- ✅ Old methods vẫn tồn tại (deprecated)
- ✅ New methods được implement song song
- ✅ Có thể migrate từ từ

### **Worker Side Changes Needed:**
```typescript
// BE Worker cần implement:
@MessagePattern({ cmd: 'user_execute_node' })
async handleUserExecuteNode(payload: UserNodeExecuteDto) {
  // Process với inputData
}

@MessagePattern({ cmd: 'user_execute_workflow' })
async handleUserExecuteWorkflow(payload: UserWorkflowExecuteDto) {
  // Process workflow với inputData
}
```

## ✅ **Verification Checklist**

- [ ] Webhook trigger gửi correct payload format
- [ ] Manual node execution support inputData
- [ ] Workflow execution use new format
- [ ] Trigger nodes (webhook/schedule) handled correctly
- [ ] Regular nodes sent to worker với inputData
- [ ] SSE events vẫn hoạt động bình thường
- [ ] Error handling vẫn robust
- [ ] Logging đầy đủ cho debugging

## 🎉 **Benefits**

1. **Consistent Data Structure**: `inputData` field cho tất cả data
2. **Clear Message Patterns**: Specific commands cho từng action
3. **Better Type Safety**: Strong typing với DTOs
4. **Easier Worker Implementation**: Clean, predictable payload structure
5. **Flexible Input Handling**: Support arbitrary input data cho nodes
