import { Inject, Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { ClientProxy } from '@nestjs/microservices';
import { Response } from 'express';

export interface WorkflowSSEClient {
  id: string;
  userId: number;
  workflowId?: string;
  nodeId?: string;
  response: Response;
  createdAt: Date;
  lastPing: Date;
}

export interface WorkflowNodeEvent {
  type: 'node.started' | 'node.completed' | 'node.failed' | 'node.progress' | 'workflow.completed' | 'workflow.failed' | 'test_progress';
  workflowId?: string;
  nodeId?: string;
  executionId?: string;
  userId?: number;
  data?: any;
  timestamp: string;
  progress?: number;
  error?: string;
  // Thêm fields từ BE Worker payload
  nodeResult?: {
    success: boolean;
    output: any;
    executionTime: number;
  };
  // Cho phép any additional fields từ worker
  [key: string]: any;
}

/**
 * Service quản lý Server-Sent Events cho workflow execution (User)
 * Gửi real-time updates về trạng thái thực hiện workflow và node cho user
 * Tích hợp với Redis để nhận events từ BE worker
 */
@Injectable()
export class WorkflowSSEService implements OnModuleInit {
  private readonly logger = new Logger(WorkflowSSEService.name);
  private readonly clients = new Map<string, WorkflowSSEClient>();
  private readonly pingInterval = 30000; // 30 seconds
  private pingTimer: NodeJS.Timeout;

  constructor(
    private readonly eventEmitter: EventEmitter2,
    @Inject('REDIS_CLIENT') private readonly redisClient: ClientProxy,
  ) {
    this.startPingTimer();
  }

  /**
   * Initialize Redis subscriptions khi module khởi động
   */
  async onModuleInit() {
    await this.setupRedisSubscriptions();
  }

  /**
   * Setup Redis subscriptions để nhận events từ BE worker
   */
  private async setupRedisSubscriptions(): Promise<void> {
    try {
      this.logger.log('Setting up Redis subscriptions for workflow events...');

      // Subscribe to workflow node events từ worker
      this.redisClient.send({ cmd: 'subscribe' }, 'workflow.node.events').subscribe({
        next: (data) => this.handleRedisNodeEvent(data),
        error: (error) => this.logger.error('Redis subscription error:', error),
      });

      // Subscribe to workflow events từ worker
      this.redisClient.send({ cmd: 'subscribe' }, 'workflow.events').subscribe({
        next: (data) => this.handleRedisWorkflowEvent(data),
        error: (error) => this.logger.error('Redis workflow subscription error:', error),
      });

      this.logger.log('Redis subscriptions established successfully');
    } catch (error) {
      this.logger.error('Failed to setup Redis subscriptions:', error);
    }
  }

  /**
   * Handle node events từ Redis (từ BE worker)
   */
  private handleRedisNodeEvent(data: any): void {
    try {
      this.logger.debug('Received Redis node event:', data);

      // Giữ nguyên payload từ worker, chỉ thêm timestamp nếu không có
      const event: WorkflowNodeEvent = {
        ...data, // Giữ nguyên tất cả fields từ worker
        timestamp: data.timestamp || new Date().toISOString(),
      };

      // Broadcast đến SSE clients
      this.broadcastWorkflowEvent(event);

      this.logger.debug(`Broadcasted Redis node event: ${event.type} for node ${event.nodeId}`);
    } catch (error) {
      this.logger.error('Error handling Redis node event:', error);
    }
  }

  /**
   * Handle workflow events từ Redis (từ BE worker)
   */
  private handleRedisWorkflowEvent(data: any): void {
    try {
      this.logger.debug('Received Redis workflow event:', data);

      // Parse workflow event data từ worker
      const event: WorkflowNodeEvent = {
        type: data.type, // 'workflow.completed', 'workflow.failed'
        workflowId: data.workflowId,
        executionId: data.executionId,
        userId: data.userId,
        data: data.data,
        timestamp: data.timestamp || new Date().toISOString(),
        error: data.error,
      };

      // Broadcast đến SSE clients
      this.broadcastWorkflowEvent(event);

      this.logger.debug(`Broadcasted Redis workflow event: ${event.type} for workflow ${event.workflowId}`);
    } catch (error) {
      this.logger.error('Error handling Redis workflow event:', error);
    }
  }

  /**
   * Tạo SSE connection cho client
   */
  createSSEConnection(
    userId: number,
    response: Response,
    workflowId?: string,
    nodeId?: string,
  ): string {
    const clientId = `${userId}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    response.set({
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache, no-transform',
      Connection: 'keep-alive',
      'X-Accel-Buffering': 'no',
    });

    response.flushHeaders();

    // Gửi initial connection message
    this.sendSSEMessage(response, {
      type: 'connection.established',
      data: {
        clientId,
        userId,
        workflowId,
        nodeId,
        timestamp: new Date().toISOString(),
      },
    });

    // Lưu client
    const client: WorkflowSSEClient = {
      id: clientId,
      userId,
      workflowId,
      nodeId,
      response,
      createdAt: new Date(),
      lastPing: new Date(),
    };

    this.clients.set(clientId, client);

    // Xử lý khi client disconnect
    response.on('close', () => {
      this.removeClient(clientId);
    });

    response.on('error', (error) => {
      this.logger.error(`SSE connection error for client ${clientId}:`, error);
      this.removeClient(clientId);
    });

    this.logger.log(`SSE connection established for user ${userId}, client: ${clientId}`);
    return clientId;
  }

  /**
   * Gửi message qua SSE
   */
  private sendSSEMessage(response: Response, data: any): void {
    try {
      const message = `data: ${JSON.stringify(data)}\n\n`;
      response.write(message);
    } catch (error) {
      this.logger.error('Error sending SSE message:', error);
    }
  }

  /**
   * Broadcast event đến tất cả clients phù hợp
   */
  broadcastWorkflowEvent(event: WorkflowNodeEvent): void {
    const relevantClients = Array.from(this.clients.values()).filter(client => {
      // Nếu event có userId, chỉ gửi đến clients của cùng user
      if (event.userId && client.userId !== event.userId) return false;

      // Nếu client đang theo dõi workflow cụ thể và event có workflowId
      if (client.workflowId && event.workflowId && client.workflowId !== event.workflowId) return false;

      // Nếu client đang theo dõi node cụ thể và event có nodeId
      if (client.nodeId && event.nodeId && client.nodeId !== event.nodeId) return false;

      // Nếu event không có workflowId hoặc userId (như test_progress), gửi đến tất cả clients
      return true;
    });

    relevantClients.forEach(client => {
      try {
        // Gửi nguyên payload từ worker, chỉ wrap trong workflow.event
        this.sendSSEMessage(client.response, {
          type: 'workflow.event',
          event, // Giữ nguyên toàn bộ payload từ worker
          timestamp: new Date().toISOString(),
        });

        client.lastPing = new Date();
      } catch (error) {
        this.logger.error(`Error sending event to client ${client.id}:`, error);
        this.removeClient(client.id);
      }
    });

    this.logger.debug(`Broadcasted ${event.type} to ${relevantClients.length} clients`);
  }

  /**
   * Gửi event đến client cụ thể
   */
  sendToClient(clientId: string, event: WorkflowNodeEvent): void {
    const client = this.clients.get(clientId);
    if (!client) {
      this.logger.warn(`Client ${clientId} not found`);
      return;
    }

    try {
      this.sendSSEMessage(client.response, {
        type: 'workflow.event',
        event,
        timestamp: new Date().toISOString(),
      });

      client.lastPing = new Date();
    } catch (error) {
      this.logger.error(`Error sending event to client ${clientId}:`, error);
      this.removeClient(clientId);
    }
  }

  /**
   * Remove client khỏi danh sách
   */
  private removeClient(clientId: string): void {
    const client = this.clients.get(clientId);
    if (client) {
      try {
        client.response.end();
      } catch (error) {
        // Ignore errors when closing response
      }

      this.clients.delete(clientId);
      this.logger.log(`Removed SSE client: ${clientId}`);
    }
  }

  /**
   * Ping tất cả clients để maintain connection
   */
  private startPingTimer(): void {
    this.pingTimer = setInterval(() => {
      const now = new Date();
      const staleClients: string[] = [];

      this.clients.forEach((client, clientId) => {
        const timeSinceLastPing = now.getTime() - client.lastPing.getTime();

        if (timeSinceLastPing > this.pingInterval * 2) {
          // Client đã không phản hồi quá lâu, remove
          staleClients.push(clientId);
        } else {
          // Gửi ping
          try {
            this.sendSSEMessage(client.response, {
              type: 'ping',
              timestamp: now.toISOString(),
            });
          } catch (error) {
            staleClients.push(clientId);
          }
        }
      });

      // Remove stale clients
      staleClients.forEach(clientId => this.removeClient(clientId));

      if (this.clients.size > 0) {
        this.logger.debug(`SSE ping sent to ${this.clients.size} clients, removed ${staleClients.length} stale clients`);
      }
    }, this.pingInterval);
  }

  /**
   * Event listeners cho local workflow events (không phải từ Redis)
   * Các events từ Redis sẽ được handle bởi handleRedisNodeEvent và handleRedisWorkflowEvent
   */

  @OnEvent('workflow.local.started')
  handleLocalWorkflowStarted(payload: any): void {
    const event: WorkflowNodeEvent = {
      type: 'workflow.started' as any,
      workflowId: payload.workflowId,
      executionId: payload.executionId,
      userId: payload.userId,
      data: payload.data,
      timestamp: new Date().toISOString(),
    };

    this.broadcastWorkflowEvent(event);
    this.logger.debug(`Broadcasted local workflow started: ${payload.workflowId}`);
  }

  @OnEvent('webhook.trigger.local')
  handleLocalWebhookTrigger(payload: any): void {
    const event: WorkflowNodeEvent = {
      type: 'node.started',
      workflowId: payload.workflowId,
      nodeId: payload.nodeId,
      executionId: payload.executionId,
      userId: payload.userId,
      data: {
        trigger: 'webhook',
        webhookId: payload.webhookId,
        ...payload.data,
      },
      timestamp: new Date().toISOString(),
    };

    this.broadcastWorkflowEvent(event);
    this.logger.debug(`Broadcasted local webhook trigger: ${payload.nodeId}`);
  }

  /**
   * Method để emit event đến Redis (để worker xử lý)
   */
  async emitToRedis(eventType: string, payload: any): Promise<void> {
    try {
      await this.redisClient.send({ cmd: 'publish' }, {
        channel: 'workflow.commands',
        event: eventType,
        data: payload,
      }).toPromise();

      this.logger.debug(`Emitted to Redis: ${eventType}`, payload);
    } catch (error) {
      this.logger.error('Failed to emit to Redis:', error);
    }
  }

  /**
   * Cleanup khi service bị destroy
   */
  onModuleDestroy(): void {
    if (this.pingTimer) {
      clearInterval(this.pingTimer);
    }

    // Close tất cả connections
    this.clients.forEach((client, clientId) => {
      this.removeClient(clientId);
    });
  }

  /**
   * Get thống kê clients
   */
  getStats(): any {
    return {
      totalClients: this.clients.size,
      clientsByUser: Array.from(this.clients.values()).reduce((acc, client) => {
        acc[client.userId] = (acc[client.userId] || 0) + 1;
        return acc;
      }, {} as Record<number, number>),
    };
  }
}
