import {
  <PERSON>,
  <PERSON>,
  Param,
  Req,
  <PERSON><PERSON>,
  Body,
  Headers,
  HttpException,
  HttpStatus,
  Injectable,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { WebhookRegistry } from '../entities/webhook-registry.entity';
import { WorkflowRedisService } from '../services/workflow-redis.service';

@Controller('/webhooks/:webhookId')
export class WebhookTriggerController {
  private readonly logger = new Logger(WebhookTriggerController.name);

  constructor(
    @InjectRepository(WebhookRegistry)
    private readonly webhookRegistryRepository: Repository<WebhookRegistry>,
    private readonly eventEmitter: EventEmitter2,
    private readonly workflowRedisService: WorkflowRedisService,
  ) {}

  @All()
  async handleWebhook(
    @Param('webhookId') webhookId: string,
    @Req() request: Request,
    @Res() response: Response,
    @Body() body: any,
    @Headers() headers: Record<string, string>,
  ): Promise<void> {
    const startTime = Date.now();

    try {
      // 1. Validate webhook exists and load relations
      const webhook = await this.webhookRegistryRepository.findOne({
        where: { id: webhookId },
        relations: ['node', 'workflow'],
      });

      if (!webhook) {
        throw new HttpException(
          {
            error: 'Webhook not found',
            webhookId,
            timestamp: new Date().toISOString(),
          },
          HttpStatus.NOT_FOUND,
        );
      }

      // 2. Prepare webhook data
      const webhookData = {
        webhookId,
        webhookName: webhook.webhookName,
        nodeId: webhook.nodeId,
        workflowId: webhook.workflowId,
        method: request.method,
        headers,
        body,
        query: request.query,
        sourceIp: request.ip,
        userAgent: request.get('User-Agent'),
        contentType: request.get('Content-Type'),
        contentLength: request.get('Content-Length'),
        timestamp: new Date().toISOString(),
      };


      // 3. Emit webhook received event for waiting executions
      this.eventEmitter.emit('webhook.received', {
        webhookId,
        nodeId: webhook.nodeId,
        workflowId: webhook.workflowId,
        webhookData,
      });

      // 4. Emit local webhook trigger event for SSE (immediate feedback)
      this.eventEmitter.emit('webhook.trigger.local', {
        workflowId: webhook.workflowId,
        nodeId: webhook.nodeId,
        executionId: `webhook_${webhookId}_${Date.now()}`,
        userId: webhook.workflow?.userId || null,
        webhookId,
        data: {
          webhookName: webhook.webhookName,
          method: request.method,
          receivedAt: new Date().toISOString(),
        },
      });

      // 5. TODO: Add to queue for processing (for non-waiting executions)
      // await this.webhookQueueService.addWebhookJob(webhookData);

      // 6. Send webhook data to Redis for worker processing với new format
      try {
        await this.workflowRedisService.processWebhookNew(
          webhookId,
          webhook.workflowId,
          webhook.nodeId,
          webhook.workflow?.userId || 0,
          webhookData,
        );
        this.logger.log(`Webhook sent to worker successfully: ${webhookId}`);
      } catch (redisError) {
        this.logger.error('Failed to send webhook to Redis worker:', redisError);
        // Continue với response, không block user
      }

      // 7. Return immediate response
      const responseTime = Date.now() - startTime;
      
      response.status(200).json({
        status: 'received',
        message: 'Webhook processed successfully',
        webhookId,
        webhookName: webhook.webhookName,
        timestamp: new Date().toISOString(),
        responseTime: `${responseTime}ms`,
        webhook: {
          id: webhook.id,
          name: webhook.webhookName,
          nodeId: webhook.nodeId,
          workflowId: webhook.workflowId,
          nodeName: webhook.node?.name,
          workflowName: webhook.workflow?.name,
        },
        request: {
          method: request.method,
          contentType: request.get('Content-Type'),
          bodyReceived: !!body,
          queryParams: Object.keys(request.query || {}).length,
          headers: Object.keys(headers).length,
          sourceIp: request.ip,
          userAgent: request.get('User-Agent'),
        },
      });

      console.log(`🚀 Webhook response sent:`, {
        webhookId,
        status: 200,
        responseTime: `${responseTime}ms`,
      });

    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      console.error(`💥 Webhook error:`, {
        webhookId,
        error: error.message,
        stack: error.stack,
        ip: request.ip,
        method: request.method,
        responseTime: `${responseTime}ms`,
        timestamp: new Date().toISOString(),
      });

      if (error instanceof HttpException) {
        response.status(error.getStatus()).json(error.getResponse());
      } else {
        response.status(500).json({
          error: 'Internal server error',
          webhookId,
          message: 'An unexpected error occurred while processing the webhook',
          timestamp: new Date().toISOString(),
          responseTime: `${responseTime}ms`,
        });
      }
    }
  }
}
