import { JwtUtilService } from '@modules/auth/guards/jwt.util';
import {
  Controller,
  Get,
  Logger,
  Param,
  Query,
  Req,
  Res,
} from '@nestjs/common';
import { ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Request, Response } from 'express';
import { WorkflowSSEService } from '../services/workflow-sse.service';

/**
 * Controller xử lý Server-Sent Events cho workflow execution
 * Cung cấp real-time updates về trạng thái thực hiện workflow và node
 */
@ApiTags('Workflow SSE')
@Controller('workflows/sse')
export class WorkflowSSEController {
  private readonly logger = new Logger(WorkflowSSEController.name);

  constructor(
    private readonly workflowSSEService: WorkflowSSEService,
    private readonly jwtUtilService: JwtUtilService,
  ) { }

  /**
   * Tạo SSE connection để theo dõi workflow cụ thể với query token authentication
   */
  @Get('workflows/:workflowId/events')
  @ApiOperation({
    summary: 'Tạo SSE connection cho workflow cụ thể với query token',
    description: 'Tạo Server-Sent Events connection với JWT token trong query parameter để tương thích với EventSource API',
  })
  @ApiParam({
    name: 'workflowId',
    description: 'ID của workflow cần theo dõi',
    example: 'wf_123456789',
  })
  @ApiQuery({
    name: 'token',
    description: 'JWT token for authentication',
    required: true,
    type: 'string',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  @ApiResponse({
    status: 200,
    description: 'SSE connection established for specific workflow',
    content: {
      'text/event-stream': {
        schema: {
          type: 'string',
          example: 'data: {"type":"connection.established","data":{"clientId":"123_1234567890_abc","userId":123,"workflowId":"wf_123","timestamp":"2025-01-01T00:00:00.000Z"}}\n\n',
        },
      },
    },
  })
  async streamWorkflowEvents(
    @Param('workflowId') workflowId: string,
    @Query('token') token: string,
    @Res() response: Response,
    @Req() request: Request,
  ): Promise<void> {
    this.logger.log(`Creating SSE connection with query token for workflow ${workflowId}`);

    try {
      // Validate token manually
      if (!token) {
        response.status(401).json({
          error: 'Unauthorized',
          message: 'Token is required in query parameter',
        });
        return;
      }

      // Validate JWT token
      let userId: number;
      try {
        const payload = this.jwtUtilService.verifyTokenUser(token);
        userId = payload.id;
        this.logger.log(`Token validated successfully for user ${userId}`);
      } catch (error) {
        this.logger.error('Token validation failed:', error.message);
        response.status(401).json({
          error: 'Unauthorized',
          message: 'Invalid or expired token',
        });
        return;
      }

      const clientId = this.workflowSSEService.createSSEConnection(
        userId,
        response,
        workflowId,
      );

      this.logger.log(`SSE connection created with query token for user ${userId}, workflow ${workflowId}, client: ${clientId}`);
    } catch (error) {
      this.logger.error(`Error creating SSE connection with token:`, error);
      if (!response.headersSent) {
        response.status(500).json({
          error: 'Internal Server Error',
          message: 'Failed to establish SSE connection',
        });
      }
    }
  }
}
