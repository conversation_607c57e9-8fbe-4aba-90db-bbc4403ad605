import { JwtUserGuard } from '@/modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtUtilService } from '@modules/auth/guards/jwt.util';
import {
  Controller,
  Get,
  HttpStatus,
  Logger,
  Param,
  Query,
  Req,
  Res,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Request, Response } from 'express';
import { WorkflowSSEService } from '../services/workflow-sse.service';

/**
 * Controller xử lý Server-Sent Events cho workflow execution
 * Cung cấp real-time updates về trạng thái thực hiện workflow và node
 */
@ApiTags('Workflow SSE')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@Controller('workflows/sse')
export class WorkflowSSEController {
  private readonly logger = new Logger(WorkflowSSEController.name);

  constructor(
    private readonly workflowSSEService: WorkflowSSEService,
    private readonly jwtUtilService: JwtUtilService,
  ) { }

  /**
   * Tạo SSE connection để theo dõi workflow cụ thể
   */
  @Get('workflows/:workflowId/events')
  @ApiOperation({
    summary: 'Tạo SSE connection cho workflow cụ thể',
    description: 'Tạo Server-Sent Events connection để nhận real-time updates về workflow execution cụ thể',
  })
  @ApiParam({
    name: 'workflowId',
    description: 'ID của workflow cần theo dõi',
    example: 'wf_123456789',
  })
  @ApiResponse({
    status: 200,
    description: 'SSE connection established for specific workflow',
    content: {
      'text/event-stream': {
        schema: {
          type: 'string',
          example: 'data: {"type":"connection.established","data":{"clientId":"123_1234567890_abc","userId":123,"workflowId":"wf_123","timestamp":"2025-01-01T00:00:00.000Z"}}\n\n',
        },
      },
    },
  })
  async streamWorkflowEvents(
    @Param('workflowId') workflowId: string,
    @CurrentUser('id') userId: number,
    @Res() response: Response,
  ): Promise<void> {
    this.logger.log(`Creating SSE connection for user ${userId} - workflow ${workflowId}`);

    const clientId = this.workflowSSEService.createSSEConnection(
      userId,
      response,
      workflowId,
    );

    this.logger.log(`SSE connection created for user ${userId}, workflow ${workflowId}, client: ${clientId}`);
  }

  /**
   * Tạo SSE connection để theo dõi node cụ thể trong workflow
   */
  @Get('workflows/:workflowId/nodes/:nodeId/events')
  @ApiOperation({
    summary: 'Tạo SSE connection cho node cụ thể',
    description: 'Tạo Server-Sent Events connection để nhận real-time updates về node execution cụ thể',
  })
  @ApiParam({
    name: 'workflowId',
    description: 'ID của workflow',
    example: 'wf_123456789',
  })
  @ApiParam({
    name: 'nodeId',
    description: 'ID của node cần theo dõi',
    example: 'node_123456789',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'SSE stream established successfully',
    headers: {
      'Content-Type': { description: 'text/event-stream' },
      'Cache-Control': { description: 'no-cache' },
      Connection: { description: 'keep-alive' },
    },
  })
  async streamNodeEvents(
    @Param('workflowId') workflowId: string,
    @Param('nodeId') nodeId: string,
    @CurrentUser('id') userId: number,
    @Res() response: Response,
  ): Promise<void> {
    this.logger.log(`Creating SSE connection for user ${userId} - workflow ${workflowId}, node ${nodeId}`);

    const clientId = this.workflowSSEService.createSSEConnection(
      userId,
      response,
      workflowId,
      nodeId,
    );

    this.logger.log(`SSE connection created for user ${userId}, workflow ${workflowId}, node ${nodeId}, client: ${clientId}`);
  }

  /**
   * Tạo SSE connection với token trong query parameter (cho EventSource compatibility)
   */
  @Get('workflows/:workflowId/stream')
  @ApiOperation({
    summary: 'Tạo SSE connection với query token (Admin)',
    description: 'Tạo Server-Sent Events connection với JWT token trong query parameter để tương thích với EventSource API',
  })
  @ApiParam({
    name: 'workflowId',
    description: 'ID của workflow cần theo dõi',
    example: 'wf_123456789',
  })
  @ApiQuery({
    name: 'token',
    description: 'JWT token for authentication',
    required: true,
    type: 'string',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  @ApiResponse({
    status: 200,
    description: 'SSE connection established successfully',
    content: {
      'text/event-stream': {
        schema: {
          type: 'string',
          example: 'data: {"type":"connection.established","data":{"clientId":"123_1234567890_abc","userId":123,"workflowId":"wf_123","timestamp":"2025-01-01T00:00:00.000Z"}}\n\n',
        },
      },
    },
  })
  async streamWorkflowEventsWithToken(
    @Param('workflowId') workflowId: string,
    @Query('token') token: string,
    @Res() response: Response,
    @Req() request: Request,
  ): Promise<void> {
    this.logger.log(`Creating admin SSE connection with query token for workflow ${workflowId}`);

    try {
      // Validate token manually
      if (!token) {
        response.status(401).json({
          error: 'Unauthorized',
          message: 'Token is required in query parameter',
        });
        return;
      }

      // Validate JWT token
      let userId: number;
      try {
        const payload = this.jwtUtilService.verifyTokenUser(token);
        userId = payload.id;
        this.logger.log(`Admin token validated successfully for user ${userId}`);
      } catch (error) {
        this.logger.error('Admin token validation failed:', error.message);
        response.status(401).json({
          error: 'Unauthorized',
          message: 'Invalid or expired token',
        });
        return;
      }

      const clientId = this.workflowSSEService.createSSEConnection(
        userId,
        response,
        workflowId,
      );

      this.logger.log(`Admin SSE connection created with query token for user ${userId}, workflow ${workflowId}, client: ${clientId}`);
    } catch (error) {
      this.logger.error(`Error creating admin SSE connection with token:`, error);
      if (!response.headersSent) {
        response.status(500).json({
          error: 'Internal Server Error',
          message: 'Failed to establish SSE connection',
        });
      }
    }
  }
}
