# 🧪 Updated SSE Test Commands

## 🎯 **Endpoints Đ<PERSON> <PERSON><PERSON><PERSON>**

### **Admin Endpoints:**
```
GET /v1/admin/workflows/sse/workflows/{workflowId}/events?token={admin_jwt_token}
GET /v1/admin/workflows/sse/workflows/{workflowId}/stream?token={admin_jwt_token}
```

### **User Endpoints:**
```
GET /v1/user/workflows/sse/workflows/{workflowId}/events?token={user_jwt_token}
GET /v1/user/workflows/sse/workflows/{workflowId}/stream?token={user_jwt_token}
```

## 🧪 **Test Commands**

### **1. Test Admin SSE (Original endpoint với query token):**
```bash
# Get admin token
ADMIN_TOKEN=$(curl -X POST http://localhost:3003/v1/auth/admin/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "admin_password"}' \
  | jq -r '.data.accessToken')

# Test admin SSE với /events endpoint
curl -N -H "Accept: text/event-stream" \
  "http://localhost:3003/v1/admin/workflows/sse/workflows/6c340d9f-ba0b-44b4-8497-676e052dd773/events?token=${ADMIN_TOKEN}"

# Test admin SSE với /stream endpoint  
curl -N -H "Accept: text/event-stream" \
  "http://localhost:3003/v1/admin/workflows/sse/workflows/6c340d9f-ba0b-44b4-8497-676e052dd773/stream?token=${ADMIN_TOKEN}"
```

### **2. Test User SSE:**
```bash
# Get user token
USER_TOKEN=$(curl -X POST http://localhost:3003/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "user_password"}' \
  | jq -r '.data.accessToken')

# Test user SSE với /events endpoint
curl -N -H "Accept: text/event-stream" \
  "http://localhost:3003/v1/user/workflows/sse/workflows/wf-test-123/events?token=${USER_TOKEN}"

# Test user SSE với /stream endpoint
curl -N -H "Accept: text/event-stream" \
  "http://localhost:3003/v1/user/workflows/sse/workflows/wf-test-123/stream?token=${USER_TOKEN}"
```

## 🔧 **Postman Test Setup**

### **Admin SSE Test:**
1. **Method**: GET
2. **URL**: `http://localhost:3003/v1/admin/workflows/sse/workflows/6c340d9f-ba0b-44b4-8497-676e052dd773/events`
3. **Query Parameters**:
   - Key: `token`
   - Value: `{your_admin_token}`
4. **Headers**:
   - `Accept`: `text/event-stream`
   - `Cache-Control`: `no-cache`

### **User SSE Test:**
1. **Method**: GET
2. **URL**: `http://localhost:3003/v1/user/workflows/sse/workflows/wf-test-123/events`
3. **Query Parameters**:
   - Key: `token`
   - Value: `{your_user_token}`
4. **Headers**:
   - `Accept`: `text/event-stream`

## 📊 **Expected Responses**

### **Admin SSE Success:**
```
HTTP/1.1 200 OK
Content-Type: text/event-stream
Cache-Control: no-cache
Connection: keep-alive

data: {"type":"connection.established","data":{"clientId":"admin_1_1706612345_abc","employeeId":1,"workflowId":"6c340d9f-ba0b-44b4-8497-676e052dd773","timestamp":"2025-07-30T07:04:39.000Z"}}
```

### **User SSE Success:**
```
HTTP/1.1 200 OK
Content-Type: text/event-stream
Cache-Control: no-cache
Connection: keep-alive

data: {"type":"connection.established","data":{"clientId":"123_1706612345_abc","userId":123,"workflowId":"wf-test-123","timestamp":"2025-07-30T07:04:39.000Z"}}
```

## 🚨 **Error Responses**

### **401 Unauthorized:**
```json
{
  "error": "Unauthorized",
  "message": "Token is required in query parameter"
}
```

### **401 Invalid Token:**
```json
{
  "error": "Unauthorized", 
  "message": "Invalid or expired token"
}
```

### **500 Internal Server Error:**
```json
{
  "error": "Internal Server Error",
  "message": "Failed to establish SSE connection"
}
```

## 🔍 **Debug Commands**

### **Check Server Logs:**
```bash
# Monitor SSE connections
tail -f logs/application.log | grep -E "(SSE|workflow|token|connection)"
```

### **Expected Log Patterns:**
```
[WorkflowSSEController] Creating SSE connection with query token for workflow 6c340d9f-ba0b-44b4-8497-676e052dd773
[WorkflowSSEController] Token validated successfully for user 123
[WorkflowSSEService] Creating SSE connection for user 123 - workflow 6c340d9f-ba0b-44b4-8497-676e052dd773
[WorkflowSSEController] SSE connection created with query token for user 123, workflow 6c340d9f-ba0b-44b4-8497-676e052dd773, client: 123_1706612345_abc
```

## 🎯 **Browser Console Test**

### **Admin Test:**
```javascript
const adminToken = 'YOUR_ADMIN_TOKEN';
const workflowId = '6c340d9f-ba0b-44b4-8497-676e052dd773';

const adminSSE = new EventSource(
  `http://localhost:3003/v1/admin/workflows/sse/workflows/${workflowId}/events?token=${adminToken}`
);

adminSSE.onopen = () => console.log('✅ Admin SSE Connected');
adminSSE.onmessage = (e) => console.log('📡 Admin Event:', JSON.parse(e.data));
adminSSE.onerror = (e) => console.error('❌ Admin SSE Error:', e);
```

### **User Test:**
```javascript
const userToken = 'YOUR_USER_TOKEN';
const workflowId = 'wf-test-123';

const userSSE = new EventSource(
  `http://localhost:3003/v1/user/workflows/sse/workflows/${workflowId}/events?token=${userToken}`
);

userSSE.onopen = () => console.log('✅ User SSE Connected');
userSSE.onmessage = (e) => console.log('📡 User Event:', JSON.parse(e.data));
userSSE.onerror = (e) => console.error('❌ User SSE Error:', e);
```

## ✅ **Success Checklist**

- [ ] Admin `/events` endpoint returns 200 OK
- [ ] Admin `/stream` endpoint returns 200 OK  
- [ ] User `/events` endpoint returns 200 OK
- [ ] User `/stream` endpoint returns 200 OK
- [ ] Connection established events received
- [ ] Server logs show successful token validation
- [ ] No 401/500 errors in response
- [ ] EventSource API works in browser

## 🎉 **Implementation Summary**

✅ **Both admin and user endpoints updated** với query token authentication  
✅ **No more header authentication issues** với EventSource  
✅ **Proper token validation** cho cả admin và user tokens  
✅ **Comprehensive error handling** với meaningful error messages  
✅ **Full EventSource compatibility** cho browser integration  

**Tất cả SSE endpoints bây giờ đều support query token authentication!** 🚀
