import {
  Controller,
  Post,
  Body,
  UseGuards,
  Logger,
  Query,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiBody, ApiQuery } from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { WorkerSimulationService } from '../services/worker-simulation.service';

/**
 * Controller để simulate BE Worker events
 * CHỈ DÀNH CHO DEVELOPMENT VÀ TESTING
 */
@ApiTags('Worker Simulation (Development Only)')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@Controller('workflows/worker-simulation')
export class WorkerSimulationController {
  private readonly logger = new Logger(WorkerSimulationController.name);

  constructor(private readonly workerSimulationService: WorkerSimulationService) {}

  /**
   * Simulate test_progress event từ worker
   */
  @Post('test-progress')
  @ApiOperation({
    summary: 'Simulate test_progress event từ worker',
    description: 'Gửi test_progress event giống như BE Worker để test SSE',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        nodeId: {
          type: 'string',
          description: 'ID của node',
          example: 'node_2',
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Test progress event simulated',
  })
  async simulateTestProgress(
    @CurrentUser('id') userId: number,
    @Body() body: { nodeId?: string },
  ): Promise<any> {
    const { nodeId = 'node_2' } = body;

    this.logger.log(`User ${userId} triggered test_progress simulation for node ${nodeId}`);

    await this.workerSimulationService.simulateTestProgress(nodeId);

    return {
      message: 'Test progress event simulated',
      nodeId,
      userId,
      payload: {
        type: 'test_progress',
        nodeId,
        nodeResult: {
          success: true,
          output: '...',
          executionTime: 1500,
        },
      },
    };
  }

  /**
   * Simulate full workflow execution từ worker
   */
  @Post('full-workflow')
  @ApiOperation({
    summary: 'Simulate full workflow execution từ worker',
    description: 'Simulate toàn bộ workflow execution với multiple nodes',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        workflowId: {
          type: 'string',
          description: 'ID của workflow',
          example: 'demo-workflow-123',
        },
      },
      required: ['workflowId'],
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Full workflow simulation started',
  })
  async simulateFullWorkflow(
    @CurrentUser('id') userId: number,
    @Body() body: { workflowId: string },
  ): Promise<any> {
    const { workflowId } = body;

    this.logger.log(`User ${userId} triggered full workflow simulation: ${workflowId}`);

    // Trigger simulation (non-blocking)
    this.workerSimulationService.simulateFullWorkflowExecution(workflowId, userId);

    return {
      message: 'Full workflow simulation started',
      workflowId,
      userId,
      estimatedDuration: '10 seconds',
      nodes: ['node_1', 'node_2', 'node_3'],
    };
  }

  /**
   * Simulate node progress từ worker
   */
  @Post('node-progress')
  @ApiOperation({
    summary: 'Simulate node progress từ worker',
    description: 'Gửi node progress event để test real-time updates',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        workflowId: {
          type: 'string',
          description: 'ID của workflow',
          example: 'demo-workflow-123',
        },
        nodeId: {
          type: 'string',
          description: 'ID của node',
          example: 'node_1',
        },
        progress: {
          type: 'number',
          description: 'Progress percentage (0-100)',
          example: 75,
        },
      },
      required: ['workflowId', 'nodeId', 'progress'],
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Node progress event simulated',
  })
  async simulateNodeProgress(
    @CurrentUser('id') userId: number,
    @Body() body: { workflowId: string; nodeId: string; progress: number },
  ): Promise<any> {
    const { workflowId, nodeId, progress } = body;

    this.logger.log(`User ${userId} triggered node progress simulation: ${workflowId}/${nodeId} - ${progress}%`);

    await this.workerSimulationService.simulateNodeProgress(workflowId, nodeId, userId, progress);

    return {
      message: 'Node progress event simulated',
      workflowId,
      nodeId,
      progress,
      userId,
    };
  }

  /**
   * Simulate custom payload từ worker
   */
  @Post('custom-payload')
  @ApiOperation({
    summary: 'Simulate custom payload từ worker',
    description: 'Gửi custom payload để test SSE với bất kỳ data nào',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        payload: {
          type: 'object',
          description: 'Custom payload object',
          example: {
            type: 'custom_event',
            nodeId: 'node_custom',
            customData: {
              message: 'Custom message from worker',
              value: 42,
            },
          },
        },
        channel: {
          type: 'string',
          description: 'Redis channel to publish to',
          example: 'workflow.node.events',
        },
      },
      required: ['payload'],
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Custom payload simulated',
  })
  async simulateCustomPayload(
    @CurrentUser('id') userId: number,
    @Body() body: { payload: any; channel?: string },
  ): Promise<any> {
    const { payload, channel = 'workflow.node.events' } = body;

    this.logger.log(`User ${userId} triggered custom payload simulation:`, payload);

    await this.workerSimulationService.simulateCustomPayload(payload, channel);

    return {
      message: 'Custom payload simulated',
      payload,
      channel,
      userId,
    };
  }

  /**
   * Simulate exact test_progress payload như bạn cung cấp
   */
  @Post('exact-test-progress')
  @ApiOperation({
    summary: 'Simulate exact test_progress payload',
    description: 'Gửi chính xác payload test_progress như bạn cung cấp',
  })
  @ApiResponse({
    status: 200,
    description: 'Exact test progress payload simulated',
  })
  async simulateExactTestProgress(
    @CurrentUser('id') userId: number,
  ): Promise<any> {
    // Payload chính xác như bạn cung cấp
    const exactPayload = {
      type: 'test_progress',
      nodeId: 'node_2',
      nodeResult: {
        success: true,
        output: {
          message: 'Processing completed successfully',
          data: [1, 2, 3, 4, 5],
          metadata: {
            timestamp: new Date().toISOString(),
            version: '1.0.0',
          },
        },
        executionTime: 1500,
      },
    };

    this.logger.log(`User ${userId} triggered exact test_progress simulation`);

    await this.workerSimulationService.simulateCustomPayload(exactPayload, 'workflow.node.events');

    return {
      message: 'Exact test_progress payload simulated',
      payload: exactPayload,
      userId,
    };
  }
}
