import {
  <PERSON>,
  Get,
  Res,
  UseGuards,
  Lo<PERSON>,
  <PERSON>m,
  Query,
  Req,
} from '@nestjs/common';
import { Response, Request } from 'express';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiQuery } from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { WorkflowSSEService } from '../services/workflow-sse-user.service';
import { JwtUtilService } from '@modules/auth/guards/jwt.util';

/**
 * Controller xử lý Server-Sent Events cho workflow execution (User)
 * Cung cấp real-time updates về trạng thái thực hiện workflow và node cho user
 */
@ApiTags('User Workflow SSE')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@Controller('user/workflows/sse')
export class WorkflowSSEUserController {
  private readonly logger = new Logger(WorkflowSSEUserController.name);

  constructor(
    private readonly workflowSSEService: WorkflowSSEService,
    private readonly jwtUtilService: JwtUtilService,
  ) {}

  /**
   * Tạo SSE connection để theo dõi workflow cụ thể và tất cả các node bên trong
   */
  @Get('workflows/:workflowId/events')
  @ApiOperation({
    summary: 'Tạo SSE connection cho workflow và tất cả nodes',
    description: 'Tạo Server-Sent Events connection để nhận real-time updates về workflow execution và tất cả node executions bên trong workflow',
  })
  @ApiParam({
    name: 'workflowId',
    description: 'ID của workflow cần theo dõi',
    example: 'wf_123456789',
  })
  @ApiQuery({
    name: 'token',
    description: 'JWT token for authentication (required for SSE)',
    required: false,
    type: 'string',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  @ApiResponse({
    status: 200,
    description: 'SSE connection established for workflow and all its nodes',
    content: {
      'text/event-stream': {
        schema: {
          type: 'string',
          example: 'data: {"type":"connection.established","data":{"clientId":"123_1234567890_abc","userId":123,"workflowId":"wf_123","timestamp":"2025-01-01T00:00:00.000Z"}}\n\n',
        },
      },
    },
  })
  async streamWorkflowEvents(
    @Param('workflowId') workflowId: string,
    @CurrentUser('id') userId: number,
    @Res() response: Response,
  ): Promise<void> {
    this.logger.log(`Creating SSE connection for user ${userId} - workflow ${workflowId} (includes all nodes)`);

    const clientId = this.workflowSSEService.createSSEConnection(
      userId,
      response,
      workflowId,
    );

    this.logger.log(`SSE connection created for user ${userId}, workflow ${workflowId} (all nodes), client: ${clientId}`);
  }

  /**
   * Tạo SSE connection với token trong query parameter (cho EventSource compatibility)
   */
  @Get('workflows/:workflowId/stream')
  @ApiOperation({
    summary: 'Tạo SSE connection với query token',
    description: 'Tạo Server-Sent Events connection với JWT token trong query parameter để tương thích với EventSource API',
  })
  @ApiParam({
    name: 'workflowId',
    description: 'ID của workflow cần theo dõi',
    example: 'wf_123456789',
  })
  @ApiQuery({
    name: 'token',
    description: 'JWT token for authentication',
    required: true,
    type: 'string',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  @ApiResponse({
    status: 200,
    description: 'SSE connection established successfully',
    content: {
      'text/event-stream': {
        schema: {
          type: 'string',
          example: 'data: {"type":"connection.established","data":{"clientId":"123_1234567890_abc","userId":123,"workflowId":"wf_123","timestamp":"2025-01-01T00:00:00.000Z"}}\n\n',
        },
      },
    },
  })
  async streamWorkflowEventsWithToken(
    @Param('workflowId') workflowId: string,
    @Query('token') token: string,
    @Res() response: Response,
    @Req() request: Request,
  ): Promise<void> {
    this.logger.log(`Creating SSE connection with query token for workflow ${workflowId}`);

    try {
      // Validate token manually
      if (!token) {
        response.status(401).json({
          error: 'Unauthorized',
          message: 'Token is required in query parameter',
        });
        return;
      }

      // Validate JWT token
      let userId: number;
      try {
        const payload = this.jwtUtilService.verifyTokenUser(token);
        userId = payload.id;
        this.logger.log(`Token validated successfully for user ${userId}`);
      } catch (error) {
        this.logger.error('Token validation failed:', error.message);
        response.status(401).json({
          error: 'Unauthorized',
          message: 'Invalid or expired token',
        });
        return;
      }

      const clientId = this.workflowSSEService.createSSEConnection(
        userId,
        response,
        workflowId,
      );

      this.logger.log(`SSE connection created with query token for user ${userId}, workflow ${workflowId}, client: ${clientId}`);
    } catch (error) {
      this.logger.error(`Error creating SSE connection with token:`, error);
      if (!response.headersSent) {
        response.status(500).json({
          error: 'Internal Server Error',
          message: 'Failed to establish SSE connection',
        });
      }
    }
  }

}
