import {
  <PERSON>,
  Get,
  Res,
  UseGuards,
  Lo<PERSON>,
  <PERSON>m,
  Req,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { Response, Request } from 'express';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam } from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { WorkflowSSEService } from '../services/workflow-sse-user.service';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';

/**
 * Controller xử lý Server-Sent Events cho workflow execution (User)
 * Cung cấp real-time updates về trạng thái thực hiện workflow và node cho user
 */
@ApiTags('User Workflow SSE')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@Controller('user/workflows/sse')
export class WorkflowSSEUserController {
  private readonly logger = new Logger(WorkflowSSEUserController.name);

  constructor(
    private readonly workflowSSEService: WorkflowSSEService,
  ) {}

  /**
   * Stream workflow events via Server-Sent Events
   */
  @Get('workflows/:workflowId/events')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Stream workflow events via Server-Sent Events',
    description: 'Establishes an SSE connection to stream real-time workflow events and all node executions.',
  })
  @ApiParam({
    name: 'workflowId',
    description: 'ID của workflow cần theo dõi',
    example: 'wf_123456789',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'SSE stream established successfully',
    headers: {
      'Content-Type': { description: 'text/event-stream' },
      'Cache-Control': { description: 'no-cache' },
      Connection: { description: 'keep-alive' },
    },
  })
  @ApiErrorResponse()
  async streamWorkflowEvents(
    @CurrentUser() user: JwtPayload,
    @Param('workflowId') workflowId: string,
    @Req() req: Request,
    @Res() res: Response,
  ): Promise<void> {
    const logger = new Logger('WorkflowSSEUserController');

    try {
      // Handle client disconnect
      req.on('close', () => {
        logger.log(`🔌 Client disconnected from workflow ${workflowId}`);
      });

      req.on('error', (error) => {
        logger.error(`🔥 Request error for workflow ${workflowId}:`, error);
      });

      // Delegate to workflow SSE service
      const clientId = this.workflowSSEService.createSSEConnection(
        user.id,
        res,
        workflowId,
      );

      this.logger.log(`SSE connection created for user ${user.id}, workflow ${workflowId}, client: ${clientId}`);
    } catch (error) {
      logger.error(`💥 Controller error for workflow ${workflowId}:`, error);

      // Send error response if headers haven't been sent yet
      if (!res.headersSent && !res.writableEnded) {
        try {
          res.status(500).json({
            error: 'Internal server error',
            message: 'Failed to establish SSE stream',
            workflowId,
          });
        } catch (responseError) {
          logger.error(
            `Failed to send error response for workflow ${workflowId}:`,
            responseError,
          );
        }
      }
    }
  }
}
