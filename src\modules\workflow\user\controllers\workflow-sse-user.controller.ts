import {
  <PERSON>,
  Get,
  Res,
  UseGuards,
  Lo<PERSON>,
  Param,
} from '@nestjs/common';
import { Response } from 'express';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam } from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { WorkflowSSEService } from '../services/workflow-sse-user.service';

/**
 * Controller xử lý Server-Sent Events cho workflow execution (User)
 * Cung cấp real-time updates về trạng thái thực hiện workflow và node cho user
 */
@ApiTags('User Workflow SSE')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@Controller('user/workflows/sse')
export class WorkflowSSEUserController {
  private readonly logger = new Logger(WorkflowSSEUserController.name);

  constructor(private readonly workflowSSEService: WorkflowSSEService) {}

  /**
   * Tạo SSE connection để theo dõi workflow cụ thể và tất cả các node bên trong
   */
  @Get('workflows/:workflowId/events')
  @ApiOperation({
    summary: 'Tạo SSE connection cho workflow và tất cả nodes',
    description: 'Tạo Server-Sent Events connection để nhận real-time updates về workflow execution và tất cả node executions bên trong workflow',
  })
  @ApiParam({
    name: 'workflowId',
    description: 'ID của workflow cần theo dõi',
    example: 'wf_123456789',
  })
  @ApiResponse({
    status: 200,
    description: 'SSE connection established for workflow and all its nodes',
    content: {
      'text/event-stream': {
        schema: {
          type: 'string',
          example: 'data: {"type":"connection.established","data":{"clientId":"123_1234567890_abc","userId":123,"workflowId":"wf_123","timestamp":"2025-01-01T00:00:00.000Z"}}\n\n',
        },
      },
    },
  })
  async streamWorkflowEvents(
    @Param('workflowId') workflowId: string,
    @CurrentUser('id') userId: number,
    @Res() response: Response,
  ): Promise<void> {
    this.logger.log(`Creating SSE connection for user ${userId} - workflow ${workflowId} (includes all nodes)`);

    const clientId = this.workflowSSEService.createSSEConnection(
      userId,
      response,
      workflowId,
    );

    this.logger.log(`SSE connection created for user ${userId}, workflow ${workflowId} (all nodes), client: ${clientId}`);
  }


}
