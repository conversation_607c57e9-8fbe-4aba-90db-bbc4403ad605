# 🔐 SSE Authentication Guide - Workflow Module

## ❌ **Vấn Đề: EventSource Không Support Custom Headers**

### **Tại sao SSE authentication khó khăn?**

```javascript
// ❌ CÁCH NÀY KHÔNG HOẠT ĐỘNG
const eventSource = new EventSource('/api/user/workflows/sse/workflows/wf-123/events', {
    headers: {
        'Authorization': 'Bearer your-jwt-token'  // Browser sẽ ignore header này!
    }
});
```

**EventSource API của browser không hỗ trợ custom headers!** Đây là limitation cố hữu của Web API.

## ✅ **Giải Pháp: Query Parameter Authentication**

### **Endpoints Mới:**

#### **User Endpoint:**
```
GET /api/user/workflows/sse/workflows/{workflowId}/stream?token={user_jwt_token}
```

#### **Admin Endpoint:**
```
GET /api/admin/workflows/sse/workflows/{workflowId}/stream?token={admin_jwt_token}
```

### **Cách Sử Dụng:**

#### **1. Frontend JavaScript:**
```javascript
const workflowId = 'wf-test-123';
const jwtToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...';

// ✅ CÁCH NÀY HOẠT ĐỘNG
const eventSource = new EventSource(
    `/api/user/workflows/sse/workflows/${workflowId}/stream?token=${jwtToken}`
);

eventSource.onopen = function(event) {
    console.log('✅ SSE Connection opened');
};

eventSource.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('📡 Workflow Event:', data);
    
    // Handle different event types
    switch (data.type) {
        case 'connection.established':
            console.log('🔗 Connection established:', data.data);
            break;
        case 'workflow.started':
            console.log('🚀 Workflow started:', data.data);
            break;
        case 'node.started':
            console.log('⚡ Node started:', data.data);
            break;
        case 'node.completed':
            console.log('✅ Node completed:', data.data);
            break;
        case 'workflow.completed':
            console.log('🎉 Workflow completed:', data.data);
            break;
        default:
            console.log('📨 Other event:', data);
    }
};

eventSource.onerror = function(error) {
    console.error('❌ SSE Error:', error);
    
    // Handle different error states
    if (eventSource.readyState === EventSource.CLOSED) {
        console.log('🔌 Connection closed');
    } else if (eventSource.readyState === EventSource.CONNECTING) {
        console.log('🔄 Reconnecting...');
    }
};

// Cleanup when done
function cleanup() {
    if (eventSource) {
        eventSource.close();
        console.log('🧹 SSE Connection closed');
    }
}

// Auto cleanup on page unload
window.addEventListener('beforeunload', cleanup);
```

#### **2. React Hook Example:**
```typescript
import { useEffect, useState, useRef } from 'react';

interface WorkflowEvent {
    type: string;
    data: any;
    timestamp: string;
}

export function useWorkflowSSE(workflowId: string, token: string) {
    const [events, setEvents] = useState<WorkflowEvent[]>([]);
    const [connectionStatus, setConnectionStatus] = useState<'disconnected' | 'connecting' | 'connected'>('disconnected');
    const eventSourceRef = useRef<EventSource | null>(null);

    useEffect(() => {
        if (!workflowId || !token) return;

        const url = `/api/user/workflows/sse/workflows/${workflowId}/stream?token=${token}`;
        
        setConnectionStatus('connecting');
        const eventSource = new EventSource(url);
        eventSourceRef.current = eventSource;

        eventSource.onopen = () => {
            setConnectionStatus('connected');
        };

        eventSource.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                setEvents(prev => [...prev, {
                    type: data.type,
                    data: data.data || data,
                    timestamp: new Date().toISOString()
                }]);
            } catch (error) {
                console.error('Failed to parse SSE event:', error);
            }
        };

        eventSource.onerror = (error) => {
            console.error('SSE Error:', error);
            setConnectionStatus('disconnected');
        };

        return () => {
            eventSource.close();
            setConnectionStatus('disconnected');
        };
    }, [workflowId, token]);

    const disconnect = () => {
        if (eventSourceRef.current) {
            eventSourceRef.current.close();
            eventSourceRef.current = null;
            setConnectionStatus('disconnected');
        }
    };

    return {
        events,
        connectionStatus,
        disconnect
    };
}
```

#### **3. Vue.js Composition API:**
```typescript
import { ref, onMounted, onUnmounted } from 'vue';

export function useWorkflowSSE(workflowId: string, token: string) {
    const events = ref<any[]>([]);
    const connectionStatus = ref<string>('disconnected');
    let eventSource: EventSource | null = null;

    const connect = () => {
        if (!workflowId || !token) return;

        const url = `/api/user/workflows/sse/workflows/${workflowId}/stream?token=${token}`;
        
        connectionStatus.value = 'connecting';
        eventSource = new EventSource(url);

        eventSource.onopen = () => {
            connectionStatus.value = 'connected';
        };

        eventSource.onmessage = (event) => {
            const data = JSON.parse(event.data);
            events.value.push(data);
        };

        eventSource.onerror = () => {
            connectionStatus.value = 'disconnected';
        };
    };

    const disconnect = () => {
        if (eventSource) {
            eventSource.close();
            eventSource = null;
            connectionStatus.value = 'disconnected';
        }
    };

    onMounted(connect);
    onUnmounted(disconnect);

    return {
        events,
        connectionStatus,
        connect,
        disconnect
    };
}
```

## 🧪 **Testing Commands**

### **1. Test với cURL:**

#### **User SSE Connection:**
```bash
# Get user JWT token first
USER_TOKEN=$(curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}' \
  | jq -r '.data.accessToken')

# Test user SSE connection
curl -N -H "Accept: text/event-stream" \
  "http://localhost:3000/api/user/workflows/sse/workflows/wf-test-123/stream?token=${USER_TOKEN}"
```

#### **Admin SSE Connection:**
```bash
# Get admin JWT token first
ADMIN_TOKEN=$(curl -X POST http://localhost:3000/api/auth/admin/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "admin_password"}' \
  | jq -r '.data.accessToken')

# Test admin SSE connection
curl -N -H "Accept: text/event-stream" \
  "http://localhost:3000/api/admin/workflows/sse/workflows/6c340d9f-ba0b-44b4-8497-676e052dd773/stream?token=${ADMIN_TOKEN}"
```

### **2. Test với Browser Console:**
```javascript
// Paste this in browser console
const token = 'YOUR_JWT_TOKEN_HERE';
const workflowId = 'wf-test-123';

const sse = new EventSource(`/api/user/workflows/sse/workflows/${workflowId}/stream?token=${token}`);

sse.onmessage = (e) => console.log('Event:', JSON.parse(e.data));
sse.onerror = (e) => console.error('Error:', e);

// To close: sse.close();
```

## 🔧 **Troubleshooting**

### **Common Issues:**

1. **401 Unauthorized:**
   - Check if JWT token is valid and not expired
   - Ensure token is properly URL encoded if it contains special characters

2. **Connection Immediately Closes:**
   - Check server logs for authentication errors
   - Verify workflow ID exists and user has access

3. **No Events Received:**
   - Ensure workflow is actually running
   - Check if events are being emitted by triggering workflow execution

### **Debug Commands:**
```bash
# Check server logs
tail -f logs/application.log | grep -E "(SSE|workflow|token)"

# Test token validity
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:3000/api/user/workflows
```

## 📊 **Comparison: Old vs New Approach**

| **Aspect** | **Old (Header Auth)** | **New (Query Auth)** |
|------------|----------------------|---------------------|
| **Browser Support** | ❌ Not supported | ✅ Fully supported |
| **Security** | ✅ Headers more secure | ⚠️ Token in URL (use HTTPS) |
| **Implementation** | ❌ Complex workarounds | ✅ Simple and direct |
| **EventSource API** | ❌ Incompatible | ✅ Native support |
| **Debugging** | ❌ Hard to debug | ✅ Easy to test |

## 🔒 **Security Considerations**

1. **Always use HTTPS** in production to protect token in URL
2. **Short token expiry** to minimize exposure risk
3. **Server-side validation** of token on every connection
4. **Rate limiting** to prevent abuse
5. **Proper CORS configuration** for cross-origin requests

## ✅ **Implementation Complete!**

Bạn có thể sử dụng endpoint mới này để connect SSE với authentication thông qua query parameter, tương thích hoàn toàn với EventSource API của browser!
